/* 翻译小窗口样式 */
.translate-window-container {
    width: 100%;
    height: 100vh;
    display: flex;
    flex-direction: column;
    background: var(--bg-color, #ffffff);
    color: var(--text-color, #333333);
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    overflow: hidden;
}

/* 顶部工具栏 */
.translate-window-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: var(--header-bg, #f8f9fa);
    border-bottom: 1px solid var(--border-color, #e9ecef);
    -webkit-app-region: drag;
    user-select: none;
}

.window-title {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 13px;
    font-weight: 500;
    color: var(--text-color, #333);
}

.title-icon {
    font-size: 14px;
}

.window-controls {
    display: flex;
    gap: 4px;
    -webkit-app-region: no-drag;
}

.window-control-btn {
    width: 24px;
    height: 24px;
    border: none;
    border-radius: 4px;
    background: transparent;
    color: var(--text-color, #666);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.window-control-btn:hover {
    background: var(--hover-bg, #e9ecef);
}

.window-control-btn.close:hover {
    background: #ff5f56;
    color: white;
}

.window-control-btn.active {
    background: var(--primary-color, #007bff);
    color: white;
}

/* 原文输入区域 */
.translate-input-section {
    padding: 12px;
    border-bottom: 1px solid var(--border-color, #e9ecef);
}

.input-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.section-title {
    font-size: 12px;
    font-weight: 500;
    color: var(--text-secondary, #666);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.clear-btn {
    width: 20px;
    height: 20px;
    border: none;
    border-radius: 3px;
    background: transparent;
    color: var(--text-secondary, #999);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.clear-btn:hover {
    background: var(--hover-bg, #f0f0f0);
    color: var(--text-color, #333);
}

.translate-textarea {
    width: 100%;
    min-height: 80px;
    max-height: 120px;
    padding: 8px;
    border: 1px solid var(--border-color, #ddd);
    border-radius: 6px;
    font-size: 14px;
    line-height: 1.4;
    resize: vertical;
    background: var(--input-bg, #ffffff);
    color: var(--text-color, #333);
    transition: border-color 0.2s ease;
}

.translate-textarea:focus {
    outline: none;
    border-color: var(--primary-color, #007bff);
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.1);
}

/* 控制栏 */
.translate-controls {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    background: var(--controls-bg, #f8f9fa);
    border-bottom: 1px solid var(--border-color, #e9ecef);
    gap: 8px;
}

.controls-left {
    flex: 1;
    min-width: 0;
}

.controls-center {
    flex-shrink: 0;
}

.controls-right {
    flex-shrink: 0;
}

/* 翻译模型选择器 */
.translate-model-selector {
    display: flex;
    gap: 4px;
    flex-wrap: wrap;
}

.translate-model-btn {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    border: 1px solid var(--border-color, #ddd);
    border-radius: 4px;
    background: var(--button-bg, #ffffff);
    color: var(--text-color, #333);
    font-size: 11px;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
}

.translate-model-btn:hover {
    background: var(--hover-bg, #f0f0f0);
    border-color: var(--primary-color, #007bff);
}

.translate-model-btn.active {
    background: var(--primary-color, #007bff);
    color: white;
    border-color: var(--primary-color, #007bff);
}

.translate-model-btn .service-icon {
    width: 12px;
    height: 12px;
    font-size: 10px;
}

.translate-model-btn .model-name {
    font-weight: 500;
}

/* 翻译按钮 */
.translate-btn {
    width: 36px;
    height: 36px;
    border: none;
    border-radius: 50%;
    background: var(--primary-color, #007bff);
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.2);
}

.translate-btn:hover {
    background: var(--primary-hover, #0056b3);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
}

.translate-btn:active {
    transform: translateY(0);
}

.translate-btn:disabled {
    background: var(--disabled-bg, #ccc);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* 语言选择器 */
.language-selector-container {
    display: flex;
    align-items: center;
    gap: 4px;
}

.language-selector {
    position: relative;
}

.language-select {
    display: flex;
    align-items: center;
    padding: 4px 8px;
    border: 1px solid var(--border-color, #ddd);
    border-radius: 4px;
    background: var(--button-bg, #ffffff);
    color: var(--text-color, #333);
    font-size: 11px;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
}

.language-select:hover {
    background: var(--hover-bg, #f0f0f0);
    border-color: var(--primary-color, #007bff);
}

.language-text {
    display: flex;
    align-items: center;
    gap: 4px;
}

.language-emoji {
    font-size: 12px;
}

.language-label {
    font-weight: 500;
}

.language-swap-btn {
    width: 24px;
    height: 24px;
    border: none;
    border-radius: 4px;
    background: transparent;
    color: var(--text-secondary, #666);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.language-swap-btn:hover {
    background: var(--hover-bg, #f0f0f0);
    color: var(--primary-color, #007bff);
}

/* 语言选择菜单 */
.language-select-menu {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--menu-bg, #ffffff);
    border: 1px solid var(--border-color, #ddd);
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    max-height: 200px;
    overflow-y: auto;
    display: none;
}

.language-select-menu.show {
    display: block;
}

.language-option {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    font-size: 12px;
}

.language-option:hover {
    background: var(--hover-bg, #f0f0f0);
}

.language-option.selected {
    background: var(--primary-color, #007bff);
    color: white;
}

/* 翻译结果区域 */
.translate-results-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    border-bottom: 1px solid var(--border-color, #e9ecef);
}

.results-actions {
    display: flex;
    gap: 4px;
}

.action-btn {
    width: 24px;
    height: 24px;
    border: none;
    border-radius: 4px;
    background: transparent;
    color: var(--text-secondary, #666);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.action-btn:hover {
    background: var(--hover-bg, #f0f0f0);
    color: var(--primary-color, #007bff);
}

/* 翻译结果列表 */
.translate-results-list {
    flex: 1;
    overflow-y: auto;
    padding: 8px;
}

.no-results-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: var(--text-secondary, #999);
    text-align: center;
}

.placeholder-icon {
    font-size: 32px;
    margin-bottom: 8px;
    opacity: 0.5;
}

.placeholder-text {
    font-size: 13px;
    line-height: 1.4;
}

/* 翻译结果项 */
.translate-result-item {
    margin-bottom: 8px;
    border: 1px solid var(--border-color, #e9ecef);
    border-radius: 8px;
    background: var(--card-bg, #ffffff);
    overflow: hidden;
    transition: all 0.2s ease;
}

.translate-result-item:hover {
    border-color: var(--primary-color, #007bff);
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.1);
}

.result-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    background: var(--result-header-bg, #f8f9fa);
    border-bottom: 1px solid var(--border-color, #e9ecef);
}

.result-service-info {
    display: flex;
    align-items: center;
    gap: 6px;
}

.service-icon {
    font-size: 14px;
}

.service-name {
    font-size: 12px;
    font-weight: 500;
    color: var(--text-color, #333);
}

.result-status {
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 10px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 500;
}

.result-status.pending {
    background: #ffeaa7;
    color: #d63031;
}

.result-status.streaming {
    background: #74b9ff;
    color: white;
}

.result-status.completed {
    background: #00b894;
    color: white;
}

.result-status.failed {
    background: #fd79a8;
    color: white;
}

.result-actions {
    display: flex;
    gap: 4px;
}

.copy-btn {
    width: 24px;
    height: 24px;
    border: none;
    border-radius: 4px;
    background: transparent;
    color: var(--text-secondary, #666);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.copy-btn:hover {
    background: var(--hover-bg, #f0f0f0);
    color: var(--primary-color, #007bff);
}

.result-content {
    padding: 12px;
    font-size: 13px;
    line-height: 1.5;
    color: var(--text-color, #333);
    white-space: pre-wrap;
    word-wrap: break-word;
    min-height: 40px;
}

.result-content.empty {
    color: var(--text-secondary, #999);
    font-style: italic;
}

/* 模态框样式 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
}

.modal-content {
    background: var(--modal-bg, #ffffff);
    border-radius: 8px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    max-width: 400px;
    width: 90%;
    max-height: 80vh;
    overflow: hidden;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid var(--border-color, #e9ecef);
}

.modal-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
    color: var(--text-color, #333);
}

.modal-close-btn {
    width: 28px;
    height: 28px;
    border: none;
    border-radius: 4px;
    background: transparent;
    color: var(--text-secondary, #666);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.modal-close-btn:hover {
    background: var(--hover-bg, #f0f0f0);
    color: var(--text-color, #333);
}

.modal-body {
    padding: 16px;
    max-height: 60vh;
    overflow-y: auto;
}

.model-select-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

/* 动画效果 */
@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.spinning {
    animation: spin 1s linear infinite;
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
    .translate-window-container {
        --bg-color: #1a1a1a;
        --text-color: #ffffff;
        --text-secondary: #999999;
        --header-bg: #2d2d2d;
        --controls-bg: #2d2d2d;
        --border-color: #404040;
        --button-bg: #2d2d2d;
        --hover-bg: #404040;
        --input-bg: #2d2d2d;
        --card-bg: #2d2d2d;
        --result-header-bg: #404040;
        --menu-bg: #2d2d2d;
        --modal-bg: #2d2d2d;
        --primary-color: #0d6efd;
        --primary-hover: #0b5ed7;
        --disabled-bg: #666666;
    }
}
