<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>翻译小窗口测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #007AFF;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        .test-button:hover {
            background: #0056CC;
        }
        .test-result {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 6px;
            border-left: 4px solid #007AFF;
        }
        .error {
            border-left-color: #FF3B30;
            background: #fff5f5;
        }
        .success {
            border-left-color: #34C759;
            background: #f0fff4;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>翻译小窗口功能测试</h1>
        
        <h2>测试项目</h2>
        <button class="test-button" onclick="testConfigAccess()">测试配置访问</button>
        <button class="test-button" onclick="testTranslateModels()">测试翻译模型获取</button>
        <button class="test-button" onclick="testTranslateAPI()">测试翻译API</button>
        <button class="test-button" onclick="openTranslateWindow()">打开翻译小窗口</button>
        
        <div id="test-results"></div>
    </div>

    <!-- 模拟uTools环境 -->
    <script>
        // 模拟uTools API
        window.utools = {
            dbStorage: {
                getItem: (key) => localStorage.getItem(key),
                setItem: (key, value) => localStorage.setItem(key, value)
            },
            copyText: (text) => {
                navigator.clipboard.writeText(text);
                console.log('复制文本:', text);
            },
            showNotification: (message) => {
                console.log('通知:', message);
                alert(message);
            },
            createBrowserWindow: (url, options, callback) => {
                console.log('创建窗口:', url, options);
                const newWindow = window.open(url, '_blank', 'width=480,height=600');
                if (callback) callback();
                return newWindow;
            }
        };

        // 模拟ocrAPI
        window.ocrAPI = {
            db: {
                get: (id) => {
                    const stored = localStorage.getItem('test-' + id);
                    return stored ? JSON.parse(stored) : null;
                },
                put: (doc) => {
                    localStorage.setItem('test-' + doc._id, JSON.stringify(doc));
                    return { ok: true };
                }
            }
        };

        // 创建测试配置
        const testConfig = {
            _id: 'ocr-config',
            translateModels: [
                {
                    service: 'alibaba',
                    model: 'qwen-mt-turbo',
                    type: 'ai',
                    isDefault: true
                },
                {
                    service: 'alibaba',
                    model: 'qwen-mt-plus',
                    type: 'ai',
                    isDefault: true
                }
            ],
            alibaba: {
                apiKey: 'test-api-key',
                model: 'qwen-mt-turbo'
            }
        };
        
        // 保存测试配置
        window.ocrAPI.db.put(testConfig);

        function showResult(message, type = 'info') {
            const resultsDiv = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong><br>${message}`;
            resultsDiv.appendChild(resultDiv);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        function testConfigAccess() {
            try {
                const config = window.ocrAPI.db.get('ocr-config');
                if (config) {
                    showResult(`配置访问成功！<br>翻译模型数量: ${config.translateModels?.length || 0}`, 'success');
                } else {
                    showResult('配置访问失败：未找到配置', 'error');
                }
            } catch (error) {
                showResult(`配置访问失败：${error.message}`, 'error');
            }
        }

        function testTranslateModels() {
            try {
                const config = window.ocrAPI.db.get('ocr-config');
                const translateModels = config?.translateModels || [];
                
                if (translateModels.length > 0) {
                    const modelList = translateModels.map(m => `${m.service}:${m.model}`).join(', ');
                    showResult(`翻译模型获取成功！<br>模型列表: ${modelList}`, 'success');
                } else {
                    showResult('翻译模型获取失败：没有配置的模型', 'error');
                }
            } catch (error) {
                showResult(`翻译模型获取失败：${error.message}`, 'error');
            }
        }

        function testTranslateAPI() {
            showResult('翻译API测试功能待实现', 'info');
        }

        function openTranslateWindow() {
            try {
                const windowUrl = 'src/translate-window/translate-window.html';
                const newWindow = window.utools.createBrowserWindow(windowUrl, {
                    width: 480,
                    height: 600
                });
                
                if (newWindow) {
                    showResult('翻译小窗口打开成功！', 'success');
                } else {
                    showResult('翻译小窗口打开失败', 'error');
                }
            } catch (error) {
                showResult(`翻译小窗口打开失败：${error.message}`, 'error');
            }
        }

        // 页面加载完成后自动测试配置
        window.addEventListener('load', () => {
            showResult('测试环境初始化完成', 'success');
            testConfigAccess();
        });
    </script>
</body>
</html>
