// 翻译小窗口主逻辑
class TranslateWindow {
    constructor() {
        // 翻译状态管理
        this.selectedTranslateModels = [];
        this.translateResults = {};
        this.currentLanguages = {
            source: { langCode: 'auto', label: '自动检测', emoji: '🌐' },
            target: { langCode: 'zh-cn', label: '中文 (简)', emoji: '🇨🇳' }
        };
        this.isTranslating = false;
        this.isPinned = false;
        
        // 配置和语言数据
        this.config = null;
        this.languageConfig = null;
        this.translateModels = [];
        
        this.init();
    }
    
    async init() {
        try {
            // 加载配置和数据
            await this.loadConfigAndData();
            
            // 初始化UI
            this.initUI();
            
            // 绑定事件
            this.bindEvents();
            
            // 初始化翻译模型选择器
            this.initTranslateModelSelector();
            
            // 初始化语言选择器
            this.initLanguageSelectors();
            
            console.log('翻译小窗口初始化完成');
        } catch (error) {
            console.error('翻译小窗口初始化失败:', error);
            this.showNotification('初始化失败: ' + error.message, 'error');
        }
    }
    
    async loadConfigAndData() {
        try {
            // 加载配置
            this.config = await window.translateWindowAPI.getConfig();
            console.log('翻译小窗口配置加载完成:', this.config);

            // 加载语言配置
            this.languageConfig = await window.translateWindowAPI.getLanguageConfig();

            // 加载翻译模型列表
            this.translateModels = await window.translateWindowAPI.getTranslateModels();
            console.log('翻译模型列表:', this.translateModels);

            // 恢复用户选择的翻译模型
            this.loadSelectedTranslateModels();
        } catch (error) {
            console.error('加载配置和数据失败:', error);
            // 设置默认值
            this.config = {};
            this.languageConfig = { translateLanguageOptions: [] };
            this.translateModels = [];
        }
    }
    
    initUI() {
        // 设置初始语言显示
        this.updateLanguageDisplay();
        
        // 设置占位符
        const sourceTextarea = document.getElementById('translate-source-text');
        if (sourceTextarea) {
            sourceTextarea.placeholder = '请输入要翻译的文本...';
        }
    }
    
    bindEvents() {
        // 窗口控制按钮
        this.bindWindowControls();
        
        // 翻译相关事件
        this.bindTranslateEvents();
        
        // 语言选择器事件
        this.bindLanguageEvents();
        
        // 模型选择事件
        this.bindModelEvents();
        
        // 其他UI事件
        this.bindUIEvents();
    }
    
    bindWindowControls() {
        // 置顶按钮
        const pinBtn = document.getElementById('window-pin-btn');
        if (pinBtn) {
            pinBtn.addEventListener('click', () => this.togglePin());
        }
        
        // 最小化按钮
        const minimizeBtn = document.getElementById('window-minimize-btn');
        if (minimizeBtn) {
            minimizeBtn.addEventListener('click', () => this.minimizeWindow());
        }
        
        // 关闭按钮
        const closeBtn = document.getElementById('window-close-btn');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => this.closeWindow());
        }
    }
    
    bindTranslateEvents() {
        // 翻译按钮
        const translateBtn = document.getElementById('translate-execute-btn');
        if (translateBtn) {
            translateBtn.addEventListener('click', () => this.executeTranslation());
        }
        
        // 输入框回车键翻译
        const sourceTextarea = document.getElementById('translate-source-text');
        if (sourceTextarea) {
            sourceTextarea.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                    e.preventDefault();
                    this.executeTranslation();
                }
            });
        }
        
        // 清空输入按钮
        const clearBtn = document.getElementById('clear-input-btn');
        if (clearBtn) {
            clearBtn.addEventListener('click', () => this.clearInput());
        }
        
        // 复制所有结果按钮
        const copyAllBtn = document.getElementById('copy-all-btn');
        if (copyAllBtn) {
            copyAllBtn.addEventListener('click', () => this.copyAllResults());
        }
    }
    
    bindLanguageEvents() {
        // 源语言选择器
        const sourceLanguageBtn = document.getElementById('translate-source-language');
        if (sourceLanguageBtn) {
            sourceLanguageBtn.addEventListener('click', () => this.toggleLanguageMenu('source'));
        }
        
        // 目标语言选择器
        const targetLanguageBtn = document.getElementById('translate-target-language');
        if (targetLanguageBtn) {
            targetLanguageBtn.addEventListener('click', () => this.toggleLanguageMenu('target'));
        }
        
        // 语言交换按钮
        const swapBtn = document.getElementById('swap-languages-btn');
        if (swapBtn) {
            swapBtn.addEventListener('click', () => this.swapLanguages());
        }
        
        // 点击其他地方关闭语言菜单
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.language-selector')) {
                this.closeAllLanguageMenus();
            }
        });
    }
    
    bindModelEvents() {
        // 模型选择弹窗相关事件将在后续实现
    }
    
    bindUIEvents() {
        // 监听流式翻译数据
        window.translateWindowAPI.onTranslationStream((event, data) => {
            this.handleTranslationStream(data);
        });
    }
    
    // 窗口控制方法
    async togglePin() {
        try {
            this.isPinned = !this.isPinned;
            await window.translateWindowAPI.togglePin();
            
            const pinBtn = document.getElementById('window-pin-btn');
            if (pinBtn) {
                pinBtn.classList.toggle('active', this.isPinned);
                pinBtn.title = this.isPinned ? '取消置顶' : '置顶窗口';
            }
        } catch (error) {
            console.error('切换置顶状态失败:', error);
        }
    }
    
    async minimizeWindow() {
        try {
            await window.translateWindowAPI.minimizeWindow();
        } catch (error) {
            console.error('最小化窗口失败:', error);
        }
    }
    
    async closeWindow() {
        try {
            await window.translateWindowAPI.closeWindow();
        } catch (error) {
            console.error('关闭窗口失败:', error);
        }
    }
    
    // 清空输入
    clearInput() {
        const sourceTextarea = document.getElementById('translate-source-text');
        if (sourceTextarea) {
            sourceTextarea.value = '';
            sourceTextarea.focus();
        }
        
        // 清空翻译结果
        this.clearTranslateResults();
    }
    
    // 清空翻译结果
    clearTranslateResults() {
        this.translateResults = {};
        this.renderTranslateResults();
    }
    
    // 显示通知
    async showNotification(message, type = 'info') {
        try {
            await window.translateWindowAPI.showNotification(message, type);
        } catch (error) {
            console.error('显示通知失败:', error);
        }
    }
    
    // 复制文本
    async copyText(text) {
        try {
            await window.translateWindowAPI.copyText(text);
            this.showNotification('已复制到剪贴板', 'success');
        } catch (error) {
            console.error('复制失败:', error);
            this.showNotification('复制失败', 'error');
        }
    }
    
    // 复制所有翻译结果
    copyAllResults() {
        const results = Object.values(this.translateResults)
            .filter(result => result.status === 'completed' && result.result)
            .map(result => result.result)
            .join('\n\n---\n\n');
        
        if (results) {
            this.copyText(results);
        } else {
            this.showNotification('没有可复制的翻译结果', 'warning');
        }
    }
    
    // 加载用户选择的翻译模型
    loadSelectedTranslateModels() {
        // 从配置中恢复用户选择的翻译模型
        const savedModels = this.config?.selectedTranslateModels || [];
        
        if (savedModels.length > 0) {
            this.selectedTranslateModels = savedModels;
        } else if (this.translateModels.length > 0) {
            // 如果没有保存的选择，默认选择前两个模型
            this.selectedTranslateModels = this.translateModels.slice(0, 2).map(model => ({
                service: model.service,
                model: model.model,
                type: model.model === 'traditional' ? 'traditional' : 'ai',
                name: this.getServiceDisplayName(model.service)
            }));
        }
    }
    
    // 获取服务显示名称
    getServiceDisplayName(service) {
        const serviceNames = {
            'ocrpro': 'OCR Pro',
            'openai': 'OpenAI',
            'anthropic': 'Claude',
            'google': 'Gemini',
            'alibaba': '通义千问',
            'bytedance': '火山翻译',
            'baidu': '百度翻译',
            'tencent': '腾讯翻译',
            'aliyun': '阿里翻译',
            'utools': 'uTools AI'
        };
        return serviceNames[service] || service;
    }
    
    // 生成模型唯一标识
    getModelKey(service, model) {
        return `${service}:${model}`;
    }

    // 初始化翻译模型选择器
    initTranslateModelSelector() {
        this.renderTranslateModelSelector();
    }

    // 渲染翻译模型选择器
    renderTranslateModelSelector() {
        const selectorContainer = document.getElementById('translate-model-selector');
        if (!selectorContainer) return;

        if (this.translateModels.length === 0) {
            selectorContainer.innerHTML = '<div class="no-models">请先配置翻译模型</div>';
            return;
        }

        // 显示前4个可用的翻译服务
        const servicesToShow = this.translateModels.slice(0, 4);

        const buttonsHtml = servicesToShow.map(modelConfig => {
            const service = modelConfig.service;
            const model = modelConfig.model;
            const type = model === 'traditional' ? 'traditional' : 'ai';
            const isActive = this.isModelSelected(service, model, type);
            const serviceName = this.getServiceDisplayName(service);

            // 获取服务图标
            const iconHtml = this.getServiceIcon(service);

            return `
                <button type="button"
                        class="translate-model-btn ${isActive ? 'active' : ''}"
                        data-service="${service}"
                        data-model="${model}"
                        data-type="${type}"
                        title="${serviceName}"
                        onclick="window.translateWindow.toggleTranslateModel('${service}', '${model}', '${type}')">
                    ${iconHtml}
                    <span class="model-name">${serviceName}</span>
                </button>
            `;
        }).join('');

        selectorContainer.innerHTML = buttonsHtml;
    }

    // 获取服务图标
    getServiceIcon(service) {
        const icons = {
            'ocrpro': '<span class="service-icon">⚡</span>',
            'openai': '<span class="service-icon">🤖</span>',
            'anthropic': '<span class="service-icon">🧠</span>',
            'google': '<span class="service-icon">🌐</span>',
            'alibaba': '<span class="service-icon">🔮</span>',
            'bytedance': '<span class="service-icon">🔥</span>',
            'baidu': '<span class="service-icon">🐻</span>',
            'tencent': '<span class="service-icon">🐧</span>',
            'aliyun': '<span class="service-icon">☁️</span>',
            'utools': '<span class="service-icon">🛠️</span>'
        };
        return icons[service] || '<span class="service-icon">🌐</span>';
    }

    // 检查模型是否已选中
    isModelSelected(service, model, type) {
        return this.selectedTranslateModels.some(selectedModel =>
            selectedModel.service === service &&
            selectedModel.model === model &&
            selectedModel.type === type
        );
    }

    // 切换翻译模型选择状态
    toggleTranslateModel(service, model, type = 'ai') {
        const isCurrentlySelected = this.isModelSelected(service, model, type);

        if (isCurrentlySelected) {
            // 取消选择（但至少保留一个模型）
            if (this.selectedTranslateModels.length > 1) {
                this.selectedTranslateModels = this.selectedTranslateModels.filter(selectedModel =>
                    !(selectedModel.service === service && selectedModel.model === model && selectedModel.type === type)
                );
            } else {
                this.showNotification('至少需要选择一个翻译模型', 'warning');
                return;
            }
        } else {
            // 添加选择
            this.selectedTranslateModels.push({
                service: service,
                model: model,
                type: type,
                name: this.getServiceDisplayName(service)
            });
        }

        // 重新渲染选择器
        this.renderTranslateModelSelector();

        // 保存用户选择
        this.saveSelectedTranslateModels();
    }

    // 保存用户选择的翻译模型
    async saveSelectedTranslateModels() {
        try {
            const config = { ...this.config };
            config.selectedTranslateModels = this.selectedTranslateModels;
            await window.translateWindowAPI.saveConfig(config);
            this.config = config;
        } catch (error) {
            console.error('保存翻译模型选择失败:', error);
        }
    }

    // 初始化语言选择器
    initLanguageSelectors() {
        this.renderLanguageOptions('source');
        this.renderLanguageOptions('target');
        this.updateLanguageDisplay();
    }

    // 渲染语言选项
    renderLanguageOptions(type) {
        const menuId = type === 'source' ? 'translate-source-menu' : 'translate-target-menu';
        const menu = document.getElementById(menuId);
        if (!menu || !this.languageConfig) return;

        const languages = this.languageConfig.translateLanguageOptions || [];

        // 为源语言添加自动检测选项
        const optionsToShow = type === 'source'
            ? [{ langCode: 'auto', label: '自动检测', emoji: '🌐' }, ...languages]
            : languages;

        const optionsHtml = optionsToShow.map(lang => {
            const isSelected = this.currentLanguages[type].langCode === lang.langCode;
            return `
                <div class="language-option ${isSelected ? 'selected' : ''}"
                     data-lang-code="${lang.langCode}"
                     onclick="window.translateWindow.selectLanguage('${type}', '${lang.langCode}', '${lang.label}', '${lang.emoji}')">
                    <span class="language-emoji">${lang.emoji}</span>
                    <span class="language-label">${lang.label}</span>
                </div>
            `;
        }).join('');

        menu.innerHTML = optionsHtml;
    }

    // 更新语言显示
    updateLanguageDisplay() {
        // 更新源语言显示
        const sourceBtn = document.getElementById('translate-source-language');
        if (sourceBtn) {
            const lang = this.currentLanguages.source;
            sourceBtn.innerHTML = `
                <span class="language-text">
                    <span class="language-emoji">${lang.emoji}</span>
                    <span class="language-label">${lang.label}</span>
                </span>
            `;
        }

        // 更新目标语言显示
        const targetBtn = document.getElementById('translate-target-language');
        if (targetBtn) {
            const lang = this.currentLanguages.target;
            targetBtn.innerHTML = `
                <span class="language-text">
                    <span class="language-emoji">${lang.emoji}</span>
                    <span class="language-label">${lang.label}</span>
                </span>
            `;
        }
    }

    // 选择语言
    selectLanguage(type, langCode, label, emoji) {
        this.currentLanguages[type] = { langCode, label, emoji };
        this.updateLanguageDisplay();
        this.renderLanguageOptions(type); // 重新渲染以更新选中状态
        this.closeAllLanguageMenus();
    }

    // 切换语言菜单显示
    toggleLanguageMenu(type) {
        const menuId = type === 'source' ? 'translate-source-menu' : 'translate-target-menu';
        const menu = document.getElementById(menuId);
        if (!menu) return;

        // 关闭其他菜单
        this.closeAllLanguageMenus();

        // 切换当前菜单
        menu.classList.toggle('show');
    }

    // 关闭所有语言菜单
    closeAllLanguageMenus() {
        const menus = document.querySelectorAll('.language-select-menu');
        menus.forEach(menu => menu.classList.remove('show'));
    }

    // 交换源语言和目标语言
    swapLanguages() {
        if (this.currentLanguages.source.langCode === 'auto') {
            this.showNotification('自动检测语言无法交换', 'warning');
            return;
        }

        const temp = this.currentLanguages.source;
        this.currentLanguages.source = this.currentLanguages.target;
        this.currentLanguages.target = temp;

        this.updateLanguageDisplay();
        this.renderLanguageOptions('source');
        this.renderLanguageOptions('target');

        // 如果有输入文本，可以选择自动重新翻译
        const sourceText = document.getElementById('translate-source-text')?.value?.trim();
        if (sourceText && Object.keys(this.translateResults).length > 0) {
            // 可以在这里添加自动重新翻译的逻辑
        }
    }

    // 执行翻译
    async executeTranslation() {
        if (this.isTranslating) {
            this.showNotification('翻译正在进行中，请稍候...', 'warning');
            return;
        }

        const sourceTextarea = document.getElementById('translate-source-text');
        const text = sourceTextarea?.value?.trim();

        if (!text) {
            this.showNotification('请输入要翻译的文本', 'warning');
            sourceTextarea?.focus();
            return;
        }

        if (this.selectedTranslateModels.length === 0) {
            this.showNotification('请先选择翻译模型', 'error');
            return;
        }

        this.isTranslating = true;
        this.updateTranslateButton(true);

        try {
            // 语言检测（如果源语言是自动检测）
            let sourceLanguage = this.currentLanguages.source;
            if (sourceLanguage.langCode === 'auto') {
                try {
                    const detectedLang = await window.translateWindowAPI.detectLanguage(text);
                    if (detectedLang && detectedLang.langCode) {
                        sourceLanguage = detectedLang;
                    }
                } catch (error) {
                    console.warn('语言检测失败，使用默认设置:', error);
                }
            }

            const targetLanguage = this.currentLanguages.target;

            // 清空之前的结果
            this.translateResults = {};

            // 为每个选中的模型创建翻译任务
            const translationTasks = this.selectedTranslateModels.map(async (modelConfig) => {
                const modelKey = this.getModelKey(modelConfig.service, modelConfig.model);

                // 初始化结果状态
                this.translateResults[modelKey] = {
                    result: '',
                    status: 'pending',
                    error: null,
                    service: modelConfig.service,
                    model: modelConfig.model,
                    name: modelConfig.name,
                    type: modelConfig.type
                };

                // 立即渲染以显示pending状态
                this.renderTranslateResults();

                try {
                    // 更新状态为streaming
                    this.translateResults[modelKey].status = 'streaming';
                    this.renderTranslateResults();

                    // 执行翻译
                    const result = await window.translateWindowAPI.performTranslation(
                        text,
                        modelConfig.service,
                        modelConfig.model,
                        this.config,
                        targetLanguage,
                        sourceLanguage
                    );

                    if (result.success) {
                        this.translateResults[modelKey].result = result.translatedText || result.text || result.fullText || '';
                        this.translateResults[modelKey].status = 'completed';
                    } else {
                        this.translateResults[modelKey].error = result.error || '翻译失败';
                        this.translateResults[modelKey].status = 'failed';
                    }
                } catch (error) {
                    console.error(`${modelKey} 翻译失败:`, error);
                    this.translateResults[modelKey].error = error.message || '翻译失败';
                    this.translateResults[modelKey].status = 'failed';
                }

                // 更新结果显示
                this.renderTranslateResults();

                return { modelKey, result: this.translateResults[modelKey] };
            });

            // 等待所有翻译任务完成
            const results = await Promise.allSettled(translationTasks);

            // 检查翻译结果
            const successfulResults = results.filter(r =>
                r.status === 'fulfilled' && r.value.result.status === 'completed'
            );

            // 显示完成通知
            if (successfulResults.length === this.selectedTranslateModels.length) {
                this.showNotification('所有模型翻译完成', 'success');
            } else if (successfulResults.length > 0) {
                this.showNotification(`${successfulResults.length}/${this.selectedTranslateModels.length} 个模型翻译完成`, 'success');
            } else {
                this.showNotification('翻译失败', 'error');
            }

            // 保存翻译历史
            if (successfulResults.length > 0) {
                const firstSuccessResult = successfulResults[0].value.result;
                if (firstSuccessResult.result) {
                    try {
                        await window.translateWindowAPI.saveTranslateHistory(
                            text,
                            firstSuccessResult.result,
                            sourceLanguage,
                            targetLanguage
                        );
                    } catch (error) {
                        console.warn('保存翻译历史失败:', error);
                    }
                }
            }

        } catch (error) {
            console.error('翻译执行失败:', error);
            this.showNotification('翻译执行失败: ' + error.message, 'error');
        } finally {
            this.isTranslating = false;
            this.updateTranslateButton(false);
        }
    }

    // 更新翻译按钮状态
    updateTranslateButton(isTranslating) {
        const translateBtn = document.getElementById('translate-execute-btn');
        if (!translateBtn) return;

        translateBtn.disabled = isTranslating;

        if (isTranslating) {
            translateBtn.innerHTML = `
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="spinning">
                    <circle cx="12" cy="12" r="10"/>
                    <path d="M12 6v6l4 2"/>
                </svg>
            `;
            translateBtn.title = '翻译中...';
        } else {
            translateBtn.innerHTML = `
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129"/>
                </svg>
            `;
            translateBtn.title = '开始翻译';
        }
    }

    // 渲染翻译结果
    renderTranslateResults() {
        const resultsContainer = document.getElementById('translate-results-list');
        if (!resultsContainer) return;

        const resultKeys = Object.keys(this.translateResults);

        if (resultKeys.length === 0) {
            resultsContainer.innerHTML = `
                <div class="no-results-placeholder">
                    <div class="placeholder-icon">🌐</div>
                    <div class="placeholder-text">请输入文本并选择翻译模型开始翻译</div>
                </div>
            `;
            return;
        }

        const resultsHtml = resultKeys.map(modelKey => {
            const result = this.translateResults[modelKey];
            const statusClass = result.status;
            const statusText = this.getStatusText(result.status);
            const serviceIcon = this.getServiceIcon(result.service);

            return `
                <div class="translate-result-item" data-model-key="${modelKey}">
                    <div class="result-header">
                        <div class="result-service-info">
                            ${serviceIcon}
                            <span class="service-name">${result.name}</span>
                            <span class="result-status ${statusClass}">${statusText}</span>
                        </div>
                        <div class="result-actions">
                            ${result.status === 'completed' && result.result ?
                                `<button class="copy-btn" onclick="window.translateWindow.copyResult('${modelKey}')" title="复制结果">
                                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                                        <path d="M5 15H4a2 2 0 01-2-2V4a2 2 0 012-2h9a2 2 0 012 2v1"/>
                                    </svg>
                                </button>` : ''
                            }
                        </div>
                    </div>
                    <div class="result-content ${!result.result ? 'empty' : ''}">
                        ${this.getResultContent(result)}
                    </div>
                </div>
            `;
        }).join('');

        resultsContainer.innerHTML = resultsHtml;
    }

    // 获取状态文本
    getStatusText(status) {
        const statusTexts = {
            'pending': '等待中',
            'streaming': '翻译中',
            'completed': '完成',
            'failed': '失败'
        };
        return statusTexts[status] || status;
    }

    // 获取结果内容
    getResultContent(result) {
        if (result.status === 'failed') {
            return result.error || '翻译失败';
        } else if (result.status === 'pending') {
            return '等待翻译...';
        } else if (result.status === 'streaming') {
            return result.result || '翻译中...';
        } else if (result.status === 'completed') {
            return result.result || '翻译完成但无结果';
        }
        return '';
    }

    // 复制单个翻译结果
    copyResult(modelKey) {
        const result = this.translateResults[modelKey];
        if (result && result.result) {
            this.copyText(result.result);
        }
    }

    // 处理流式翻译数据
    handleTranslationStream(data) {
        const { modelKey, chunk, fullText, isComplete } = data;

        if (this.translateResults[modelKey]) {
            if (fullText !== undefined) {
                this.translateResults[modelKey].result = fullText;
            } else if (chunk) {
                this.translateResults[modelKey].result += chunk;
            }

            if (isComplete) {
                this.translateResults[modelKey].status = 'completed';
            }

            this.renderTranslateResults();
        }
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    window.translateWindow = new TranslateWindow();
});
