// 翻译小窗口预加载脚本
// 由于uTools的createBrowserWindow可能不支持标准的Electron IPC，
// 我们使用uTools的API和全局对象来实现功能

// 暴露翻译小窗口API
window.translateWindowAPI = {
    // 窗口控制
    minimizeWindow: async () => {
        if (typeof utools !== 'undefined' && utools.hideMainWindow) {
            utools.hideMainWindow();
        }
    },

    closeWindow: async () => {
        if (typeof utools !== 'undefined' && utools.outPlugin) {
            utools.outPlugin();
        } else {
            window.close();
        }
    },

    togglePin: async () => {
        // uTools窗口置顶功能
        if (typeof utools !== 'undefined' && utools.setExpendHeight) {
            // 这里可以实现置顶逻辑，但uTools API可能有限制
            console.log('切换置顶状态');
        }
        return Promise.resolve();
    },

    // 复制功能
    copyText: async (text) => {
        if (typeof utools !== 'undefined' && utools.copyText) {
            utools.copyText(text);
        } else if (navigator.clipboard) {
            await navigator.clipboard.writeText(text);
        } else {
            // 降级方案
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
        }
    },

    // 通知功能
    showNotification: async (message, type) => {
        if (typeof utools !== 'undefined' && utools.showNotification) {
            utools.showNotification(message);
        } else {
            // 使用浏览器通知API
            if (Notification.permission === 'granted') {
                new Notification('OCR Pro 翻译小窗口', { body: message });
            } else {
                console.log(`[${type}] ${message}`);
            }
        }
    },

    // 配置管理 - 使用uTools的数据存储
    getConfig: async () => {
        // 优先使用共享配置
        if (window.sharedConfig) {
            return window.sharedConfig;
        }

        if (typeof utools !== 'undefined' && utools.dbStorage) {
            const config = utools.dbStorage.getItem('ocrpro-config');
            return config ? JSON.parse(config) : {};
        }
        // 降级方案：使用localStorage
        const config = localStorage.getItem('ocrpro-config');
        return config ? JSON.parse(config) : {};
    },

    saveConfig: async (config) => {
        const configStr = JSON.stringify(config);
        if (typeof utools !== 'undefined' && utools.dbStorage) {
            utools.dbStorage.setItem('ocrpro-config', configStr);
        } else {
            localStorage.setItem('ocrpro-config', configStr);
        }
    },

    // 翻译服务 - 直接调用OCR服务
    performTranslation: async (text, service, model, config, targetLanguage, sourceLanguage) => {
        try {
            // 尝试使用共享的OCR服务实例
            let ocrServices = window.sharedOCRServices;

            // 如果没有共享实例，创建新的实例
            if (!ocrServices && typeof OCRServices !== 'undefined') {
                ocrServices = new OCRServices();
            }

            if (!ocrServices) {
                throw new Error('OCR服务不可用');
            }

            // 执行翻译
            const result = await ocrServices.performTranslation(
                text,
                service,
                model,
                config,
                null, // onStreamChunk
                targetLanguage,
                sourceLanguage
            );

            return result;
        } catch (error) {
            console.error('翻译服务调用失败:', error);
            return { success: false, error: error.message };
        }
    },

    // 语言检测
    detectLanguage: async (text) => {
        try {
            // 简单的语言检测逻辑
            const chineseRegex = /[\u4e00-\u9fff]/;
            const englishRegex = /[a-zA-Z]/;

            if (chineseRegex.test(text)) {
                return { langCode: 'zh-cn', label: '中文 (简)', emoji: '🇨🇳' };
            } else if (englishRegex.test(text)) {
                return { langCode: 'en', label: 'English', emoji: '🇺🇸' };
            } else {
                return { langCode: 'auto', label: '自动检测', emoji: '🌐' };
            }
        } catch (error) {
            console.error('语言检测失败:', error);
            return { langCode: 'auto', label: '自动检测', emoji: '🌐' };
        }
    },

    // 历史记录
    saveTranslateHistory: async (sourceText, translatedText, sourceLanguage, targetLanguage) => {
        try {
            const historyItem = {
                id: Date.now().toString(),
                sourceText,
                translatedText,
                sourceLanguage,
                targetLanguage,
                timestamp: new Date().toISOString(),
                type: 'translate'
            };

            // 获取现有历史记录
            let history = [];
            if (typeof utools !== 'undefined' && utools.dbStorage) {
                const historyStr = utools.dbStorage.getItem('ocrpro-translate-history');
                history = historyStr ? JSON.parse(historyStr) : [];
            } else {
                const historyStr = localStorage.getItem('ocrpro-translate-history');
                history = historyStr ? JSON.parse(historyStr) : [];
            }

            // 添加新记录到开头
            history.unshift(historyItem);

            // 限制历史记录数量（最多保留100条）
            if (history.length > 100) {
                history = history.slice(0, 100);
            }

            // 保存历史记录
            const historyStr = JSON.stringify(history);
            if (typeof utools !== 'undefined' && utools.dbStorage) {
                utools.dbStorage.setItem('ocrpro-translate-history', historyStr);
            } else {
                localStorage.setItem('ocrpro-translate-history', historyStr);
            }
        } catch (error) {
            console.error('保存翻译历史失败:', error);
        }
    },

    // 获取翻译模型列表
    getTranslateModels: async () => {
        try {
            const config = await window.translateWindowAPI.getConfig();

            // 构建可用的翻译模型列表
            const models = [];

            // AI服务
            const aiServices = ['openai', 'anthropic', 'google', 'alibaba', 'bytedance', 'ocrpro', 'utools'];
            aiServices.forEach(service => {
                if (config[service] && config[service].apiKey) {
                    models.push({
                        service: service,
                        model: config[service].model || 'default',
                        type: 'ai'
                    });
                }
            });

            // 传统翻译API
            const traditionalServices = ['baidu', 'tencent', 'aliyun'];
            traditionalServices.forEach(service => {
                if (config[service] && config[service].apiKey) {
                    models.push({
                        service: service,
                        model: 'traditional',
                        type: 'traditional'
                    });
                }
            });

            return models;
        } catch (error) {
            console.error('获取翻译模型列表失败:', error);
            return [];
        }
    },

    // 获取语言配置
    getLanguageConfig: async () => {
        // 返回标准的语言配置
        return {
            translateLanguageOptions: [
                { langCode: 'zh-cn', label: '中文 (简)', emoji: '🇨🇳' },
                { langCode: 'zh-tw', label: '中文 (繁)', emoji: '🇹🇼' },
                { langCode: 'en', label: 'English', emoji: '🇺🇸' },
                { langCode: 'ja', label: '日本語', emoji: '🇯🇵' },
                { langCode: 'ko', label: '한국어', emoji: '🇰🇷' },
                { langCode: 'fr', label: 'Français', emoji: '🇫🇷' },
                { langCode: 'de', label: 'Deutsch', emoji: '🇩🇪' },
                { langCode: 'es', label: 'Español', emoji: '🇪🇸' },
                { langCode: 'ru', label: 'Русский', emoji: '🇷🇺' },
                { langCode: 'pt', label: 'Português', emoji: '🇵🇹' },
                { langCode: 'it', label: 'Italiano', emoji: '🇮🇹' },
                { langCode: 'ar', label: 'العربية', emoji: '🇸🇦' },
                { langCode: 'th', label: 'ไทย', emoji: '🇹🇭' },
                { langCode: 'vi', label: 'Tiếng Việt', emoji: '🇻🇳' }
            ]
        };
    },

    // 流式翻译支持（简化实现）
    onTranslationStream: (callback) => {
        // 在实际的流式翻译中，这里会设置事件监听器
        window._translationStreamCallback = callback;
    },

    removeTranslationStreamListener: () => {
        window._translationStreamCallback = null;
    }
};

// 确保OCRServices类可用
if (typeof OCRServices === 'undefined') {
    // 如果OCRServices不可用，创建一个简化版本
    window.OCRServices = class {
        async performTranslation() {
            return { success: false, error: 'OCR服务不可用' };
        }
    };
}
