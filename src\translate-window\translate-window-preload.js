// 翻译小窗口预加载脚本
const { contextBridge, ipc<PERSON>enderer } = require('electron');

// 暴露安全的API给渲染进程
contextBridge.exposeInMainWorld('translateWindowAPI', {
    // 窗口控制
    minimizeWindow: () => ipcRenderer.invoke('window-minimize'),
    closeWindow: () => ipcRenderer.invoke('window-close'),
    togglePin: () => ipcRenderer.invoke('window-toggle-pin'),
    
    // 复制功能
    copyText: (text) => ipcRenderer.invoke('copy-text', text),
    
    // 通知功能
    showNotification: (message, type) => ipcRenderer.invoke('show-notification', message, type),
    
    // 与主窗口通信
    sendToParent: (channel, ...args) => ipcRenderer.invoke('send-to-parent', channel, ...args),
    onParentMessage: (callback) => ipcRenderer.on('parent-message', callback),
    
    // 配置管理
    getConfig: () => ipcRenderer.invoke('get-config'),
    saveConfig: (config) => ipcRenderer.invoke('save-config', config),
    
    // 翻译服务
    performTranslation: (text, service, model, config, targetLanguage, sourceLanguage) => 
        ipcRenderer.invoke('perform-translation', text, service, model, config, targetLanguage, sourceLanguage),
    
    // 语言检测
    detectLanguage: (text) => ipcRenderer.invoke('detect-language', text),
    
    // 历史记录
    saveTranslateHistory: (sourceText, translatedText, sourceLanguage, targetLanguage) =>
        ipcRenderer.invoke('save-translate-history', sourceText, translatedText, sourceLanguage, targetLanguage),
    
    // 获取翻译模型列表
    getTranslateModels: () => ipcRenderer.invoke('get-translate-models'),
    
    // 获取语言配置
    getLanguageConfig: () => ipcRenderer.invoke('get-language-config'),
    
    // 流式翻译支持
    onTranslationStream: (callback) => ipcRenderer.on('translation-stream', callback),
    removeTranslationStreamListener: () => ipcRenderer.removeAllListeners('translation-stream')
});

// 窗口加载完成事件
window.addEventListener('DOMContentLoaded', () => {
    // 通知主进程窗口已准备就绪
    ipcRenderer.send('translate-window-ready');
});
