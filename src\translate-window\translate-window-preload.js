// 翻译小窗口预加载脚本
// 由于uTools的createBrowserWindow可能不支持标准的Electron IPC，
// 我们使用uTools的API和全局对象来实现功能

// 暴露翻译小窗口API
window.translateWindowAPI = {
    // 窗口控制
    minimizeWindow: async () => {
        if (typeof utools !== 'undefined' && utools.hideMainWindow) {
            utools.hideMainWindow();
        }
    },

    closeWindow: async () => {
        if (typeof utools !== 'undefined' && utools.outPlugin) {
            utools.outPlugin();
        } else {
            window.close();
        }
    },

    togglePin: async () => {
        // uTools窗口置顶功能
        if (typeof utools !== 'undefined' && utools.setExpendHeight) {
            // 这里可以实现置顶逻辑，但uTools API可能有限制
            console.log('切换置顶状态');
        }
        return Promise.resolve();
    },

    // 复制功能
    copyText: async (text) => {
        if (typeof utools !== 'undefined' && utools.copyText) {
            utools.copyText(text);
        } else if (navigator.clipboard) {
            await navigator.clipboard.writeText(text);
        } else {
            // 降级方案
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
        }
    },

    // 通知功能
    showNotification: async (message, type) => {
        if (typeof utools !== 'undefined' && utools.showNotification) {
            utools.showNotification(message);
        } else {
            // 使用浏览器通知API
            if (Notification.permission === 'granted') {
                new Notification('OCR Pro 翻译小窗口', { body: message });
            } else {
                console.log(`[${type}] ${message}`);
            }
        }
    },

    // 配置管理 - 使用主程序的配置系统
    getConfig: async () => {
        let config = {};

        // 优先使用共享配置
        if (window.sharedConfig) {
            config = window.sharedConfig;
            console.log('使用共享配置:', config);
        }
        // 尝试使用主程序的配置管理器
        else if (window.opener && window.opener.ocrPlugin && window.opener.ocrPlugin.configManager) {
            try {
                config = window.opener.ocrPlugin.configManager.getConfig();
                console.log('从主程序配置管理器获取配置:', config);
            } catch (error) {
                console.warn('无法从主程序获取配置:', error);
            }
        }
        // 尝试直接使用ocrAPI数据库
        else if (typeof window.ocrAPI !== 'undefined' && window.ocrAPI.db) {
            try {
                config = window.ocrAPI.db.get('ocr-config') || {};
                console.log('从ocrAPI数据库获取配置:', config);
            } catch (error) {
                console.warn('无法从ocrAPI获取配置:', error);
            }
        }
        // 降级方案：使用uTools存储
        else if (typeof utools !== 'undefined' && utools.dbStorage) {
            const configStr = utools.dbStorage.getItem('ocrpro-config');
            config = configStr ? JSON.parse(configStr) : {};
            console.log('从uTools存储获取配置:', config);
        }
        // 最后降级方案：使用localStorage
        else {
            const configStr = localStorage.getItem('ocrpro-config');
            config = configStr ? JSON.parse(configStr) : {};
            console.log('从localStorage获取配置:', config);
        }

        // 确保阿里云百炼配置存在
        if (!config.alibaba) {
            config.alibaba = {
                model: '',
                useCustomModel: false,
                customModel: '',
                apiKey: '',
                baseUrl: 'https://dashscope.aliyuncs.com/compatible-mode/v1',
                maxTokens: 1000
            };
        }

        // 记录阿里云百炼配置状态
        console.log('最终配置中的阿里云百炼配置:', config.alibaba);

        return config;
    },

    saveConfig: async (config) => {
        const configStr = JSON.stringify(config);
        if (typeof utools !== 'undefined' && utools.dbStorage) {
            utools.dbStorage.setItem('ocrpro-config', configStr);
        } else {
            localStorage.setItem('ocrpro-config', configStr);
        }
    },

    // 翻译服务 - 直接调用OCR服务
    performTranslation: async (text, service, model, config, targetLanguage, sourceLanguage) => {
        try {
            // 尝试使用共享的OCR服务实例
            let ocrServices = window.sharedOCRServices;

            // 如果没有共享实例，创建新的实例
            if (!ocrServices && typeof OCRServices !== 'undefined') {
                ocrServices = new OCRServices();
            }

            if (!ocrServices) {
                throw new Error('OCR服务不可用');
            }

            // 确保配置包含所需的服务配置
            let finalConfig = config;

            // 特别处理阿里云百炼（alibaba）服务的配置
            if (service === 'alibaba') {
                console.log('处理阿里云百炼翻译请求');
                console.log('原始配置中的alibaba配置:', config.alibaba);

                // 检查阿里云百炼配置
                if (!config.alibaba || !config.alibaba.apiKey) {
                    // 尝试从共享配置获取
                    if (window.sharedConfig && window.sharedConfig.alibaba && window.sharedConfig.alibaba.apiKey) {
                        finalConfig = {
                            ...config,
                            alibaba: window.sharedConfig.alibaba
                        };
                        console.log('从共享配置获取阿里云百炼配置:', finalConfig.alibaba);
                    } else {
                        console.error('阿里云百炼API Key未配置');
                        return { success: false, error: '阿里云百炼API Key未配置，请在主程序中配置阿里云百炼服务' };
                    }
                } else {
                    console.log('使用现有的阿里云百炼配置:', config.alibaba);
                }

                // 验证最终配置
                if (!finalConfig.alibaba || !finalConfig.alibaba.apiKey) {
                    console.error('最终配置中仍然缺少阿里云百炼API Key');
                    return { success: false, error: '阿里云百炼API Key未配置，请检查主程序配置' };
                }

                console.log('最终使用的阿里云百炼配置:', finalConfig.alibaba);
            }

            // 执行翻译
            const result = await ocrServices.performTranslation(
                text,
                service,
                model,
                finalConfig,
                null, // onStreamChunk
                targetLanguage,
                sourceLanguage
            );

            return result;
        } catch (error) {
            console.error('翻译服务调用失败:', error);
            return { success: false, error: error.message };
        }
    },

    // 语言检测
    detectLanguage: async (text) => {
        try {
            // 简单的语言检测逻辑
            const chineseRegex = /[\u4e00-\u9fff]/;
            const englishRegex = /[a-zA-Z]/;

            if (chineseRegex.test(text)) {
                return { langCode: 'zh-cn', label: '中文 (简)', emoji: '🇨🇳' };
            } else if (englishRegex.test(text)) {
                return { langCode: 'en', label: 'English', emoji: '🇺🇸' };
            } else {
                return { langCode: 'auto', label: '自动检测', emoji: '🌐' };
            }
        } catch (error) {
            console.error('语言检测失败:', error);
            return { langCode: 'auto', label: '自动检测', emoji: '🌐' };
        }
    },

    // 历史记录
    saveTranslateHistory: async (sourceText, translatedText, sourceLanguage, targetLanguage) => {
        try {
            const historyItem = {
                id: Date.now().toString(),
                sourceText,
                translatedText,
                sourceLanguage,
                targetLanguage,
                timestamp: new Date().toISOString(),
                type: 'translate'
            };

            // 获取现有历史记录
            let history = [];
            if (typeof utools !== 'undefined' && utools.dbStorage) {
                const historyStr = utools.dbStorage.getItem('ocrpro-translate-history');
                history = historyStr ? JSON.parse(historyStr) : [];
            } else {
                const historyStr = localStorage.getItem('ocrpro-translate-history');
                history = historyStr ? JSON.parse(historyStr) : [];
            }

            // 添加新记录到开头
            history.unshift(historyItem);

            // 限制历史记录数量（最多保留100条）
            if (history.length > 100) {
                history = history.slice(0, 100);
            }

            // 保存历史记录
            const historyStr = JSON.stringify(history);
            if (typeof utools !== 'undefined' && utools.dbStorage) {
                utools.dbStorage.setItem('ocrpro-translate-history', historyStr);
            } else {
                localStorage.setItem('ocrpro-translate-history', historyStr);
            }
        } catch (error) {
            console.error('保存翻译历史失败:', error);
        }
    },

    // 获取翻译模型列表
    getTranslateModels: async () => {
        try {
            const config = await window.translateWindowAPI.getConfig();
            console.log('翻译小窗口获取到的配置:', config);

            // 使用主程序的translateModels配置
            const translateModels = config.translateModels || [];
            console.log('配置中的翻译模型:', translateModels);

            // 如果没有translateModels配置，返回默认模型
            if (translateModels.length === 0) {
                console.log('没有找到translateModels配置，返回默认模型');
                return [
                    {
                        service: 'alibaba',
                        model: 'qwen-mt-turbo',
                        type: 'ai'
                    },
                    {
                        service: 'alibaba',
                        model: 'qwen-mt-plus',
                        type: 'ai'
                    }
                ];
            }

            // 过滤出已配置且可用的翻译模型
            const availableModels = [];

            translateModels.forEach(modelConfig => {
                const service = modelConfig.service;
                const model = modelConfig.model;

                // 检查服务是否已配置
                const serviceConfig = config[service];

                // 对于传统翻译服务，检查是否有API密钥
                if (model === 'traditional') {
                    if (serviceConfig && (serviceConfig.apiKey || serviceConfig.accessKey)) {
                        availableModels.push({
                            service: service,
                            model: model,
                            type: 'traditional'
                        });
                    }
                } else {
                    // 对于AI服务，暂时不检查API密钥，直接添加（因为可能有默认配置）
                    availableModels.push({
                        service: service,
                        model: model,
                        type: 'ai'
                    });
                }
            });

            console.log('可用的翻译模型:', availableModels);

            // 如果仍然没有可用模型，返回默认模型
            if (availableModels.length === 0) {
                console.log('没有可用的翻译模型，返回默认模型');
                return [
                    {
                        service: 'alibaba',
                        model: 'qwen-mt-turbo',
                        type: 'ai'
                    }
                ];
            }

            return availableModels;
        } catch (error) {
            console.error('获取翻译模型列表失败:', error);
            // 返回默认模型作为降级方案
            return [
                {
                    service: 'alibaba',
                    model: 'qwen-mt-turbo',
                    type: 'ai'
                }
            ];
        }
    },

    // 获取语言配置
    getLanguageConfig: async () => {
        // 返回标准的语言配置
        return {
            translateLanguageOptions: [
                { langCode: 'zh-cn', label: '中文 (简)', emoji: '🇨🇳' },
                { langCode: 'zh-tw', label: '中文 (繁)', emoji: '🇹🇼' },
                { langCode: 'en', label: 'English', emoji: '🇺🇸' },
                { langCode: 'ja', label: '日本語', emoji: '🇯🇵' },
                { langCode: 'ko', label: '한국어', emoji: '🇰🇷' },
                { langCode: 'fr', label: 'Français', emoji: '🇫🇷' },
                { langCode: 'de', label: 'Deutsch', emoji: '🇩🇪' },
                { langCode: 'es', label: 'Español', emoji: '🇪🇸' },
                { langCode: 'ru', label: 'Русский', emoji: '🇷🇺' },
                { langCode: 'pt', label: 'Português', emoji: '🇵🇹' },
                { langCode: 'it', label: 'Italiano', emoji: '🇮🇹' },
                { langCode: 'ar', label: 'العربية', emoji: '🇸🇦' },
                { langCode: 'th', label: 'ไทย', emoji: '🇹🇭' },
                { langCode: 'vi', label: 'Tiếng Việt', emoji: '🇻🇳' }
            ]
        };
    },

    // 流式翻译支持（简化实现）
    onTranslationStream: (callback) => {
        // 在实际的流式翻译中，这里会设置事件监听器
        window._translationStreamCallback = callback;
    },

    removeTranslationStreamListener: () => {
        window._translationStreamCallback = null;
    }
};

// 确保OCRServices类可用
if (typeof OCRServices === 'undefined') {
    // 如果OCRServices不可用，创建一个简化版本
    window.OCRServices = class {
        async performTranslation() {
            return { success: false, error: 'OCR服务不可用' };
        }
    };
}
