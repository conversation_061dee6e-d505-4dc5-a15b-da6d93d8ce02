<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>翻译小窗口 - OCR Pro</title>
    <link rel="stylesheet" href="../styles.css">
    <link rel="stylesheet" href="translate-window.css">

    <!-- 加载必要的依赖 -->
    <script src="../config.js"></script>
    <script src="../ocr-services.js"></script>
    <script>
        // 确保翻译小窗口能够访问主程序的API
        if (!window.ocrAPI && window.opener && window.opener.ocrAPI) {
            window.ocrAPI = window.opener.ocrAPI;
        }
        if (!window.ConfigManager && window.opener && window.opener.ConfigManager) {
            window.ConfigManager = window.opener.ConfigManager;
        }
        if (!window.OCRServices && window.opener && window.opener.OCRServices) {
            window.OCRServices = window.opener.OCRServices;
        }
    </script>
</head>
<body>
    <div class="translate-window-container">
        <!-- 顶部工具栏 -->
        <div class="translate-window-header">
            <div class="window-title">
                <span class="title-icon">🌐</span>
                <span class="title-text">翻译小窗口</span>
            </div>
            <div class="window-controls">
                <button id="window-pin-btn" class="window-control-btn" title="置顶窗口">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                    </svg>
                </button>
                <button id="window-minimize-btn" class="window-control-btn" title="最小化">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M6 19h12"/>
                    </svg>
                </button>
                <button id="window-close-btn" class="window-control-btn close" title="关闭">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M18 6L6 18M6 6l12 12"/>
                    </svg>
                </button>
            </div>
        </div>

        <!-- 原文输入区域 -->
        <div class="translate-input-section">
            <div class="input-header">
                <span class="section-title">原文</span>
                <button id="clear-input-btn" class="clear-btn" title="清空">
                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M18 6L6 18M6 6l12 12"/>
                    </svg>
                </button>
            </div>
            <textarea id="translate-source-text" class="translate-textarea" placeholder="请输入要翻译的文本..."></textarea>
        </div>
        
        <!-- 控制栏：模型选择 + 语言选择 -->
        <div class="translate-controls">
            <div class="controls-left">
                <!-- 翻译模型快速选择 -->
                <div id="translate-model-selector" class="translate-model-selector">
                    <!-- 翻译模型按钮将在这里动态生成 -->
                </div>
            </div>
            <div class="controls-center">
                <!-- 翻译按钮 -->
                <button id="translate-execute-btn" class="translate-btn" title="开始翻译">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129"/>
                    </svg>
                </button>
            </div>
            <div class="controls-right">
                <!-- 语言选择器 -->
                <div class="language-selector-container">
                    <!-- 源语言选择器 -->
                    <div class="language-selector">
                        <button id="translate-source-language" class="language-select" title="源语言">
                            <span class="language-text">
                                <span class="language-emoji">🌐</span>
                                <span class="language-label">自动检测</span>
                            </span>
                        </button>
                        <div id="translate-source-menu" class="language-select-menu">
                            <!-- 语言选项将在这里动态生成 -->
                        </div>
                    </div>

                    <!-- 语言交换按钮 -->
                    <button id="swap-languages-btn" class="language-swap-btn" title="交换语言">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M8 3L4 7l4 4"/>
                            <path d="M4 7h16"/>
                            <path d="M16 21l4-4-4-4"/>
                            <path d="M20 17H4"/>
                        </svg>
                    </button>

                    <!-- 目标语言选择器 -->
                    <div class="language-selector">
                        <button id="translate-target-language" class="language-select" title="目标语言">
                            <span class="language-text">
                                <span class="language-emoji">🇨🇳</span>
                                <span class="language-label">中文 (简)</span>
                            </span>
                        </button>
                        <div id="translate-target-menu" class="language-select-menu">
                            <!-- 语言选项将在这里动态生成 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 翻译结果区域 -->
        <div class="translate-results-section">
            <div class="results-header">
                <span class="section-title">翻译结果</span>
                <div class="results-actions">
                    <button id="copy-all-btn" class="action-btn" title="复制所有结果">
                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                            <path d="M5 15H4a2 2 0 01-2-2V4a2 2 0 012-2h9a2 2 0 012 2v1"/>
                        </svg>
                    </button>
                </div>
            </div>
            
            <!-- 多引擎结果显示容器 -->
            <div id="translate-results-list" class="translate-results-list">
                <!-- 翻译结果将在这里动态生成 -->
                <div class="no-results-placeholder">
                    <div class="placeholder-icon">🌐</div>
                    <div class="placeholder-text">请输入文本并选择翻译模型开始翻译</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 翻译模型选择弹窗 -->
    <div id="translate-model-select-modal" class="modal-overlay" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>选择翻译模型</h3>
                <button id="close-model-modal" class="modal-close-btn">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M18 6L6 18M6 6l12 12"/>
                    </svg>
                </button>
            </div>
            <div class="modal-body">
                <div id="translate-model-select-list" class="model-select-list">
                    <!-- 模型选择列表将在这里动态生成 -->
                </div>
            </div>
        </div>
    </div>

    <script src="translate-window-preload.js"></script>
    <script src="translate-window.js"></script>
</body>
</html>
